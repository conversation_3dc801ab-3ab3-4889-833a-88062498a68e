import { useField } from 'formik';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useState } from 'react';

export const DatePicker = ({ label, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const { setValue } = helpers;
  const [selectedDate, setSelectedDate] = useState(field.value ? new Date(field.value) : null);

  const handleChange = (date) => {
    setSelectedDate(date);
    setValue(date);
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
        {label}
      </label>
      <ReactDatePicker
        {...props}
        selected={selectedDate}
        onChange={handleChange}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                 focus:outline-none focus:ring-2 focus:ring-blue-500 
                 bg-white dark:bg-gray-700 
                 text-gray-900 dark:text-gray-100"
      />
      {meta.touched && meta.error ? (
        <div className="text-red-600 dark:text-red-400 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};