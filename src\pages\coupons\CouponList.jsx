import React, { useState, useEffect } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import CouponForm from "./CouponForm";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import { Edit, Trash } from "lucide-react";
import Confirm from "@/components/ui/Confirm";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";

const CouponList = () => {
  const { t } = useTranslation();
  const [state, setState] = useState({
    page: 1,
    pageSize: 10,
    search: "",
    debouncedSearch: "",
    isModalOpen: false,
    isEditMode: false,
    currentCoupon: null,
  });

  const queryClient = useQueryClient();

  const {
    data: couponsData,
    isLoading,
    refetch,
  } = useDataFetching({
    queryKey: ["couponList", state.page, state.pageSize, state.debouncedSearch],
    endPoint: `admin/coupons?page=${state.page}&per_page=${state.pageSize}&search=${state.debouncedSearch}`,
  });

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setState((prev) => ({ ...prev, debouncedSearch: state.search }));
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [state.search]);

  const handleModalOpen = (editMode = false, coupon = null) => {
    setState((prev) => ({
      ...prev,
      isModalOpen: true,
      isEditMode: editMode,
      currentCoupon: coupon,
    }));
  };

  const handleModalClose = () => {
    setState((prev) => ({ ...prev, isModalOpen: false, currentCoupon: null }));
  };

  const handleDeleteClick = async (couponId) => {
    Confirm(async () => {
      try {
        await api.delete(`admin/coupons/${couponId}`);
        queryClient.invalidateQueries("couponList");
      } catch (error) {
        console.error(t("couponsPage.deleteError"), error);
      }
    });
  };

  const handleFormSubmit = async (values, { resetForm }) => {
    try {
      const formattedValues = {
        ...values,
        start_date: format(new Date(values.start_date), "yyyy-MM-dd"),
        end_date: format(new Date(values.end_date), "yyyy-MM-dd"),
      };
      if (state.isEditMode) {
        await api.put(
          `admin/coupons/${state.currentCoupon.id}`,
          formattedValues
        );
      } else {
        await api.post("admin/coupons", formattedValues);
      }
      queryClient.invalidateQueries("couponList");
      resetForm();
      handleModalClose();
    } catch (error) {
      console.error(t("couponsPage.submitError"), error);
    }
  };

  const columns = [
    {
      Header: t("couponsPage.code"),
      accessor: "code",
    },
    {
      Header: t("couponsPage.discountType"),
      accessor: "discount_type",
      Cell: ({ row }) => (
        <span>
          {row.original.discount_value}
          {row.original.discount_type === "percentage" ? "%" : " BDT"}
        </span>
      ),
    },
    {
      Header: t("couponsPage.appliesTo"),
      accessor: "applies_to",
      Cell: ({ row }) => (
        <span>
          {row.original.applies_to}
          {row.original.item_id
            ? ` (${t("couponsPage.item")}: ${row.original.item_id})`
            : ""}
        </span>
      ),
    },
    {
      Header: t("couponsPage.validPeriod"),
      accessor: "start_date",
      Cell: ({ row }) => (
        <span>
          {format(new Date(row.original.start_date), "dd/MM/yyyy")} -
          {format(new Date(row.original.end_date), "dd/MM/yyyy")}
        </span>
      ),
    },
    {
      Header: t("couponsPage.status"),
      accessor: "is_active",
      Cell: ({ value }) => (
        <span
          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
            value ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
          }`}
        >
          {value ? t("couponsPage.active") : t("couponsPage.inactive")}
        </span>
      ),
    },
    {
      Header: t("couponsPage.action"),
      accessor: "id",
      Cell: ({ value, row }) => (
        <div className="flex justify-center">
          <button
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg"
            onClick={() => handleModalOpen(true, row.original)}
          >
            <Edit size={16} />
          </button>
          <button
            className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg ml-2"
            onClick={() => handleDeleteClick(value)}
          >
            <Trash size={16} />
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <DataTable
        title={t("couponsPage.title")}
        columns={columns}
        data={couponsData?.data?.data || []}
        fetchData={refetch}
        loading={isLoading}
        totalPages={couponsData?.data?.total_pages || 1}
        currentPage={couponsData?.data?.current_page || 1}
        pageSize={state.pageSize}
        onPageChange={(page) => setState((prev) => ({ ...prev, page }))}
        onPageSizeChange={(pageSize) =>
          setState((prev) => ({ ...prev, pageSize }))
        }
        onSearch={(search) => setState((prev) => ({ ...prev, search }))}
        buttonLabel={t("couponsPage.addCoupon")}
        onButtonClick={() => handleModalOpen(false)}
      />

      {state.isModalOpen && (
        <Modal
          className="max-w-4xl"
          activeModal={state.isModalOpen}
          onClose={handleModalClose}
          title={
            state.isEditMode
              ? t("couponsPage.editCoupon")
              : t("couponsPage.addCoupon")
          }
        >
          <CouponForm
            initialValues={{
              code: state.currentCoupon?.code || "",
              discount_type: state.currentCoupon?.discount_type || "percentage",
              discount_value: state.currentCoupon?.discount_value || "0",
              applies_to: state.currentCoupon?.applies_to || "order",
              item_id: state.currentCoupon?.item_id || "",
              min_order_amount: state.currentCoupon?.min_order_amount || "0",
              max_discount: state.currentCoupon?.max_discount || "0",
              usage_limit: state.currentCoupon?.usage_limit || "0",
              start_date: state.currentCoupon?.start_date?.split(" ")[0] || "",
              end_date: state.currentCoupon?.end_date?.split(" ")[0] || "",
              is_active: state.currentCoupon?.is_active ? "1" : "0",
            }}
            onSubmit={handleFormSubmit}
            isEditMode={state.isEditMode}
          />
        </Modal>
      )}
    </>
  );
};

export default CouponList;
