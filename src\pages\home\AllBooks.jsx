import { useState } from "react";
import { useNavigate } from "react-router-dom";
import useDataFetching from "@/hooks/useDataFetching";
import noImage from '@/assets/noImage.png';

const BookSkeleton = () => (
  <div className="bg-white shadow-md rounded-lg overflow-hidden animate-pulse">
    <div className="w-full h-60 bg-gray-200"/>
    <div className="p-4">
      <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"/>
      <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"/>
      <div className="h-8 bg-gray-200 rounded w-24"/>
    </div>
  </div>
);

const BookList = ({ books, isLoading }) => {
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, index) => (
          <BookSkeleton key={index} />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {books.map((book) => (
        <div
          key={book.id}
          className="bg-white shadow-md rounded-lg overflow-hidden cursor-pointer"
        >
          <img
            src={book.cover_image_url || noImage}
            alt={book.title}
            className="w-full h-60 object-cover"
            onClick={() => navigate(`/ebooks/${book.id}`)}
          />
          <div className="p-4">
            <h3 className="text-lg font-medium">{book.title}</h3>
            <p className="text-gray-500 text-sm">{book.author}</p>
            <a
              href={`/ebooks/${book.id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="block mt-3 text-blue-500 font-medium hover:underline"
            >
              Read Now
            </a>
          </div>
        </div>
      ))}
    </div>
  );
};

const BookSection = ({ title, books, isLoading }) => {
  if (!isLoading && books.length === 0) return null;
  return (
    <div className="mb-10">
      {isLoading ? (
        <div className="h-8 bg-gray-200 rounded w-48 mb-4"/>
      ) : (
        <h2 className="text-2xl border-b pb-4 mb-4">{title}</h2>
      )}
      <BookList books={books} isLoading={isLoading} />
    </div>
  );
};

const BooksPage = ({ page, perPage, search }) => {
  const { data, isLoading, refetch } = useDataFetching({
    queryKey: ["allbooks", page, perPage, search],
    endPoint: "/ebooks",
    params: { page, per_page: perPage, search },
  });

  const recentBooks = data?.data?.recent || [];
  const genres = data?.data?.genres || [];

  return (
    <div className="max-w-7xl mx-auto">
      <BookSection title="Recent Books" books={recentBooks} isLoading={isLoading} />
      {(isLoading ? [...Array(2)] : genres).map((genre, index) => (
        <BookSection 
          key={isLoading ? index : genre.id}
          title={genre?.name || ''} 
          books={genre?.ebooks || []} 
          isLoading={isLoading}
        />
      ))}
    </div>
  );
};

export default BooksPage;
